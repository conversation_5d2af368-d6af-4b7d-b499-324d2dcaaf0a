<?php

namespace App\Http\Requests\Loans;

use App\Enums\Loan\LoanStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateLoanRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'borrower_id' => ['required', 'integer', 'exists:loan_customer_profiles,id'],
            'company_id' => ['required', 'integer', 'exists:companies,id'],
            'team_id' => ['required', 'integer', 'exists:teams,id'],
            'agent_id' => ['required', 'integer', 'exists:users,id'],
            'selection_type_id' => ['required', 'integer', 'exists:selections,id'],
            'status' => ['required', new Enum(LoanStatus::class)],

            // 'loan.commencement_date' => ['required', 'date', 'min:0'],
            // 'loan.next_due_date' => ['required', 'date', 'min:0'],
            // 'loan.stamping_date' => ['required', 'date', 'min:0'],
            // 'loan.last_payment' => ['required', 'numeric', 'min:0'],
            // 'loan.rebate' => ['nullable', 'numeric', 'min:0'],
            // 'loan.stamp_duty' => ['nullable', 'numeric', 'min:0'],
            // 'loan.attestation_fee' => ['nullable', 'numeric', 'min:0'],
            // 'loan.legal_fee' => ['nullable', 'numeric', 'min:0'],
            // 'loan.processing_fee' => ['nullable', 'numeric', 'min:0'],
            // 'loan.misc_fee' => ['nullable', 'numeric', 'min:0'],
        ];
    }
}
