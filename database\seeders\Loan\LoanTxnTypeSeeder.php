<?php

namespace Database\Seeders\Selections;

use App\Enums\Selection\SelectionStatus;
use App\Models\Selection;
use Database\Seeders\VersionedSeeder;

class LoanTxnTypeSeeder extends VersionedSeeder
{
    protected string $seederName = 'loan_txn_type';

    public function run(): void
    {
        $this->applyVersionedSeeds($this->getVersionedData());
    }

    protected function applyVersion(int $version, array $selectionGroups): void
    {
        foreach ($selectionGroups as $category => $selections) {
            foreach ($selections as $selection) {
                Selection::firstOrCreate(
                    [
                        'category' => $category,
                        'value' => $selection['value'],
                    ],
                    [
                        'id' => $selection['id'],
                        'description' => $selection['description'] ?? null,
                        'sort_order' => $selection['sort_order'] ?? 0,
                        'status' => $selection['status'] ?? SelectionStatus::ACTIVE,
                    ]
                );

                $this->command->line("Created selection: {$category} - {$selection['value']}");
            }
        }
    }

    protected function getVersionedData(): array
    {
        return [
            1 => [
                'state' => [
                    ['id' => 1, 'value' => 'Johor', 'description' => '', 'sort_order' => 100],
                    ['id' => 2, 'value' => 'Kedah', 'description' => '', 'sort_order' => 100],
                    ['id' => 3, 'value' => 'Kelantan', 'description' => '', 'sort_order' => 100],
                    ['id' => 4, 'value' => 'Melaka', 'description' => '', 'sort_order' => 100],
                    ['id' => 5, 'value' => 'Negeri Sembilan', 'description' => '', 'sort_order' => 100],
                    ['id' => 6, 'value' => 'Pahang', 'description' => '', 'sort_order' => 100],
                    ['id' => 7, 'value' => 'Pulau Pinang', 'description' => '', 'sort_order' => 100],
                    ['id' => 8, 'value' => 'Perak', 'description' => '', 'sort_order' => 100],
                    ['id' => 9, 'value' => 'Perlis', 'description' => '', 'sort_order' => 100],
                    ['id' => 10, 'value' => 'Sabah', 'description' => '', 'sort_order' => 100],
                    ['id' => 11, 'value' => 'Sarawak', 'description' => '', 'sort_order' => 100],
                    ['id' => 12, 'value' => 'Selangor', 'description' => '', 'sort_order' => 100],
                    ['id' => 13, 'value' => 'Terengganu', 'description' => '', 'sort_order' => 100],
                ],
                'collateral_type' => [
                    ['id' => 14, 'value' => 'Property', 'description' => '', 'sort_order' => 100],
                    ['id' => 15, 'value' => 'Other', 'description' => '', 'sort_order' => 100],
                ],
            ],
        ];
    }
}
