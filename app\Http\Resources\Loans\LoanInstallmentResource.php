<?php

namespace App\Http\Resources\Loans;

use App\Enums\Loan\LoanInstallmentStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * LoanInstallmentResource
 *
 * Transforms LoanInstallment model data for API responses.
 *
 * Usage examples:
 * - Single installment: new LoanInstallmentResource($installment)
 * - Collection: LoanInstallmentResource::collection($installments)
 * - With loan relationship: $installment->load('loan'); new LoanInstallmentResource($installment)
 */
class LoanInstallmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'loan_id' => $this->loan_id,
            'loan' => $this->whenLoaded('loan', fn () => [
                'id' => $this->loan->id,
                'uuid' => $this->loan->uuid,
                'code' => $this->loan->code,
                'status' => $this->loan->status->value,
            ]),
            'pay_date' => $this->pay_date,
            'due_date' => $this->due_date,
            'tenure' => $this->tenure,
            'total_amount' => number_format($this->total_amount ?? 0, 2),
            'principle_amount' => number_format($this->principle_amount ?? 0, 2),
            'interest_amount' => number_format($this->interest_amount ?? 0, 2),
            'late_charge_amount' => number_format($this->late_charge_amount ?? 0, 2),
            'postage_amount' => number_format($this->postage_amount ?? 0, 2),
            'outstanding_balance_amount' => number_format($this->outstanding_balance_amount ?? 0, 2),
            'status' => $this->status,
            'is_overdue' => $this->due_date < now() && $this->status === LoanInstallmentStatus::UNPAID->value,
            'days_overdue' => $this->due_date < now() && $this->status === LoanInstallmentStatus::UNPAID->value
                ? now()->diffInDays($this->due_date)
                : 0,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}
