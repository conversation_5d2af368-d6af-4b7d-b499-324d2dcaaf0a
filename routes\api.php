<?php

use App\Http\Controllers\Api\InputSearchController;
use App\Http\Controllers\Loans\LoanController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/customers/search', [InputSearchController::class, 'searchCustomers'])->name('search_customers');
Route::get('/collaterals/search', [InputSearchController::class, 'searchCollaterals'])->name('search_collaterals');
Route::get('/loans/customer/{id}/detail', [LoanController::class, 'getCustomerDetail']);
Route::get('/loans/collateral/{id}', [LoanController::class, 'getCustomerCollaterals']);
