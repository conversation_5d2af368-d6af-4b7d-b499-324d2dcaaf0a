<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use App\Traits\HasAuditFields;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Collection;
use Spatie\Permission\Models\Role as SpatieRole;

/**
 * Role model for managing roles and permissions
 */
class Role extends SpatieRole
{
    use HasAuditFields, HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'guard_name',
        'level',
        'can_see_same_level',
        'is_required_headquarter',
        'is_required_company',
        'is_required_team',
        'is_headquarter',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'level' => 'integer',
            'can_see_same_level' => 'boolean',
            'is_required_headquarter' => 'boolean',
            'is_required_company' => 'boolean',
            'is_required_team' => 'boolean',
            'is_headquarter' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Scope to exclude the super admin role.
     */
    public function scopeWithoutSuperAdmin(Builder $query): Builder
    {
        return $query->where('name', '!=', RoleName::SUPER_ADMIN);
    }

    /**
     * Scope to filter roles with level less than or equal to given value.
     */
    public function scopeMaxLevel(Builder $query, int $level): Builder
    {
        return $query->where('level', '<=', $level);
    }

    /**
     * Get all roles with level <= given level.
     */
    public static function getLowerOrEqualLevel(int $level): Collection
    {
        return self::query()
            ->maxLevel($level)
            ->get();
    }

    /**
     * Get roles for dropdown list, optionally filtered by max level.
     */
    public static function getForDropdown(?int $maxLevel = null): array
    {
        return self::query()
            ->when($maxLevel !== null, fn ($query) => $query->maxLevel($maxLevel))
            ->orderByDesc('level')
            ->get()
            ->map(fn ($role) => [
                'id' => $role->id,
                'name' => $role->name,
                'level' => $role->level,
            ])
            ->toArray();
    }

    /**
     * Get available role names up to the specified level.
     */
    public static function getAvailableRoles(int $level): Collection
    {
        return self::query()
            ->maxLevel($level)
            ->orderBy('created_at', 'asc')
            ->pluck('name');
    }
}
