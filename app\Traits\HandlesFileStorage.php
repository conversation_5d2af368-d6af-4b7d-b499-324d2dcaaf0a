<?php

namespace App\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

trait HandlesFileStorage
{
    /**
     * Store the given uploaded file in the appropriate disk.
     *
     * @return string Path where the file is stored
     */
    public function storeUploadedFile(UploadedFile $file, string $directory = 'uploads', ?string $target = null): string
    {
        $disk = $target ?? config('filesystems.default');
        $originalName = $file->getClientOriginalName();
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();

        $finalName = "$filename.$extension";
        $counter = 1;

        // Loop to handle duplicate filenames
        while (Storage::disk($disk)->exists("$directory/$finalName")) {
            $finalName = "$filename ($counter).$extension";
            $counter++;
        }

        return Storage::disk($disk)->putFileAs($directory, $file, $finalName);
    }

    public function getUploadedFile($path, ?string $target = null): array
    {

        $disk = $target ?? config('filesystems.default');

        if (! Storage::disk($disk)->exists($path)) {
            return [];
        }

        return [
            'name' => basename($path),
            'size' => Storage::disk($disk)->size($path),
            'url' => Storage::url($path),
        ];
    }

    public function storeBase64File(?array $logoInput, string $path, ?string $existingFilePath = null, ?string $target = null): ?string
    {
        $disk = $target ?? config('filesystems.default');

        if (is_null($logoInput)) {
            if ($existingFilePath && Storage::disk($disk)->exists($existingFilePath)) {
                Storage::disk($disk)->delete($existingFilePath);
            }

            return null;
        }

        if (is_array($logoInput) && isset($logoInput['base64'], $logoInput['name'])) {
            $base64String = $logoInput['base64'];
            $filename = $logoInput['name'];

            $data = preg_replace('#^data:image/\w+;base64,#i', '', $base64String);
            $data = str_replace(' ', '+', $data);
            $binary = base64_decode($data);

            if ($binary === false) {
                throw new \Exception('Base64 decode failed.');
            }

            $fullPath = "$path/$filename";

            if (
                $existingFilePath &&
                $existingFilePath !== $fullPath &&
                Storage::disk($disk)->exists($existingFilePath)
            ) {
                Storage::disk($disk)->delete($existingFilePath);
            }

            Storage::disk($disk)->put($fullPath, $binary);

            return $fullPath;
        }

        return $existingFilePath;
    }
}
