<?php

namespace App\Http\Requests\Loans;

use App\Http\Requests\BaseRequest;

class StoreLoanRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isDraft = $this->boolean('_saveAsDraft');
        $requiredOrNullable = $isDraft ? 'nullable' : 'required';

        return [
            // Personal Details
            'headquarter_id' => ['required', 'integer', 'exists:headquarters,id'],
            'company_id' => ['required', 'integer', 'exists:companies,id'],
            'team_id' => ['required', 'integer', 'exists:teams,id'],
            'agent_id' => ['required', 'integer', 'exists:agent_profiles,id'],
            'borrower_id' => ['required', 'integer', 'exists:customer_profiles,id'],
            'selection_type_id' => ['required', 'integer', 'exists:selections,id'],

            // Co-Borrower Details
            'co_borrower' => ['nullable', 'array'],
            'co_borrower.ids' => ['nullable', 'array'],
            'co_borrower.ids.*' => ['integer', 'exists:customer_profiles,id'],

            // Collateral Details
            'collateral' => ['nullable', 'array'],
            'collateral.ids' => ['nullable', 'array'],
            'collateral.ids.*' => ['integer', 'exists:customer_collaterals,id'],

            // Loan Details
            'loan.selection_mode_type_id' => [$requiredOrNullable, 'integer', 'exists:selections,id'],
            'loan.loan_principle_amount' => [$requiredOrNullable, 'numeric', 'min:0'],
            'loan.no_of_instalment' => [$requiredOrNullable, 'integer', 'min:1'],
            'loan.last_payment' => [$requiredOrNullable, 'numeric', 'min:0'],
            'loan.selection_repayment_method_id' => [$requiredOrNullable, 'integer', 'exists:selections,id'],
            'loan.instalment_amount' => [$requiredOrNullable, 'numeric', 'min:0'],
            'loan.interest' => [$requiredOrNullable, 'numeric', 'min:0'],
            'loan.late_payment_charges' => [$requiredOrNullable, 'numeric', 'min:0'],

            // Emergency Details
            // -- Personal Information
            'emergency.personal.name' => ['nullable', 'string', 'max:255'],
            'emergency.personal.identity_no' => ['nullable', 'string', 'max:255'],
            'emergency.personal.birth_date' => ['nullable', 'date'],
            'emergency.personal.age' => ['nullable', 'integer', 'min:0'],
            'emergency.personal.selection_gender_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.selection_relationship_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.selection_nationality_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.contact.selection_telephone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.contact.telephone' => ['nullable', 'string', 'max:255'],
            'emergency.personal.contact.selection_mobile_phone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.contact.mobile_phone' => ['nullable', 'string', 'max:255'],
            'emergency.personal.address.line1' => ['nullable', 'string', 'max:255'],
            'emergency.personal.address.line2' => ['nullable', 'string', 'max:255'],
            'emergency.personal.address.postcode' => ['nullable', 'string', 'max:255'],
            'emergency.personal.address.city' => ['nullable', 'string', 'max:255'],
            'emergency.personal.address.selection_state_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.personal.address.selection_country_id' => ['nullable', 'integer', 'exists:selections,id'],

            // -- Employment Information
            'emergency.employment.employment_name' => ['nullable', 'string', 'max:255'],
            'emergency.employment.contact.selection_telephone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.employment.contact.telephone' => ['nullable', 'string', 'max:255'],
            'emergency.employment.contact.selection_mobile_phone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.employment.contact.mobile_phone' => ['nullable', 'string', 'max:255'],
            'emergency.employment.address.line1' => ['nullable', 'string', 'max:255'],
            'emergency.employment.address.line2' => ['nullable', 'string', 'max:255'],
            'emergency.employment.address.postcode' => ['nullable', 'string', 'max:255'],
            'emergency.employment.address.city' => ['nullable', 'string', 'max:255'],
            'emergency.employment.address.selection_state_id' => ['nullable', 'integer', 'exists:selections,id'],
            'emergency.employment.address.selection_country_id' => ['nullable', 'integer', 'exists:selections,id'],

            // Guarantor Details
            // -- Personal Information
            'guarantor' => ['nullable', 'array'],
            'guarantor.*.personal.name' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.identity_no' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.birth_date' => ['nullable', 'date'],
            'guarantor.*.personal.age' => ['nullable', 'integer', 'min:0'],
            'guarantor.*.personal.selection_gender_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.selection_relationship_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.selection_nationality_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.contact.selection_telephone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.contact.telephone' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.contact.selection_mobile_phone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.contact.mobile_phone' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.address.line1' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.address.line2' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.address.postcode' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.address.city' => ['nullable', 'string', 'max:255'],
            'guarantor.*.personal.address.selection_state_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.personal.address.selection_country_id' => ['nullable', 'integer', 'exists:selections,id'],

            // -- Employment Information
            'guarantor.*.employment.selection_terms_of_employment_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.length_service_year' => ['nullable', 'integer', 'min:0'],
            'guarantor.*.employment.employment_name' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.length_service_month' => ['nullable', 'integer', 'min:0'],
            'guarantor.*.employment.job_position' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.selection_business_classification_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.selection_occupation_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.contact.selection_telephone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.contact.telephone' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.contact.selection_mobile_phone_country_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.contact.mobile_phone' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.address.line1' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.address.line2' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.address.postcode' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.address.city' => ['nullable', 'string', 'max:255'],
            'guarantor.*.employment.address.selection_state_id' => ['nullable', 'integer', 'exists:selections,id'],
            'guarantor.*.employment.address.selection_country_id' => ['nullable', 'integer', 'exists:selections,id'],

            // Document Details
            'document' => ['nullable', 'array'],
            'document.ids' => ['nullable', 'array'],
            'document.ids.*' => ['integer', 'exists:customer_documents,id'],
        ];
    }
}
