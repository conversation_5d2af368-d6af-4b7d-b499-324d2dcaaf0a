<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import FormInputSearch from '@/components/form/FormInputSearch.vue';
import FormInputWithDropdown from '@/components/form/FormInputWIthDropdown.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import FormTextarea from '@/components/form/FormTextarea.vue';
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { ref, watch } from 'vue';
import FormRadio from './FormRadio.vue';

interface Option {
    value: string | number;
    label: string;
}

interface Props {
    id: string;
    label: string;
    modelValue: string | number | (string | number)[] | { select: string | number; input: string };
    type:
        | 'input'
        | 'select'
        | 'password'
        | 'show'
        | 'multiselect'
        | 'textarea'
        | 'checkbox'
        | 'inputSelect'
        | 'currency'
        | 'radio'
        | 'date'
        | 'input_search';
    options?: Option[];
    placeholder?: string;
    required?: boolean;
    maxlength?: string | number;
    error?: string;
    class?: string;
    labelClass?: string;
    fieldClass?: string;
    multiple?: boolean;
    useMultiselect?: boolean;
    remark?: string;
    selectPosition?: string;
    selectPlaceholder?: string;
    api?: string;
    labelKeywords?: string[] | string;
    icons?: string[] | string;
    defaultSearchValue?: string;
    params?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    required: false,
    maxlength: 255,
    class: 'gap-2',
    labelClass: '',
    fieldClass: '',
    options: () => [],
    multiple: false,
    useMultiselect: false,
    remark: '',
});

const emit = defineEmits(['update:modelValue', 'resultsUpdated']);

const currencyValue = ref('');

watch(
    () => props.modelValue,
    (val) => {
        if (props.type === 'currency') {
            const strVal = String(val || '')
                .replace(/\D/g, '')
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            currencyValue.value = strVal;
        }
    },
    { immediate: true },
);

const onCurrencyInput = (val: string | number) => {
    const strVal = String(val);
    const numeric = strVal.replace(/\D/g, '');
    emit('update:modelValue', numeric);
};

const handleResults = (result: any) => {
    emit('resultsUpdated', result);
};
</script>

<template>
    <div :class="props.class">
        <div v-if="props.type === 'input' || props.type === 'password' || props.type === 'show'">
            <Label :for="props.id" :class="props.labelClass">
                {{ props.label }}
                <RequiredIndicator v-if="props.required" />
            </Label>
            <Input
                v-if="props.type === 'input' || props.type === 'password'"
                :id="props.id"
                :type="props.type === 'password' ? 'password' : ''"
                :model-value="props.modelValue"
                @update:model-value="(value) => emit('update:modelValue', value)"
                :required="props.required"
                :maxlength="props.maxlength"
                :placeholder="props.placeholder"
                :class="cn('mt-1 w-full', props.fieldClass)"
            />
            <p v-else-if="props.type === 'show'" class="flex h-[35px] items-center text-sm">
                {{ props.modelValue }}
            </p>
            <p v-if="props.remark !== ''" class="text-muted-foreground text-sm">
                {{ props.remark }}
            </p>
            <InputError class="mt-1" :message="props.error" />
        </div>

        <div v-else-if="type === 'checkbox'" class="flex items-center">
            <input
                :id="id"
                type="checkbox"
                :name="id"
                :checked="modelValue"
                @change="$emit('update:modelValue', $event.target.checked)"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <span v-if="remark" class="ml-2 text-sm text-gray-500">{{ remark }}</span>
        </div>

        <FormSelect
            v-else-if="props.type === 'select' || props.type === 'multiselect'"
            :id="props.id"
            :label="props.label"
            :model-value="props.modelValue"
            @update:model-value="emit('update:modelValue', $event)"
            :options="props.options"
            :placeholder="props.placeholder"
            :required="props.required"
            :error="props.error"
            :multiple="props.multiple"
            :useMultiselect="props.useMultiselect"
            :remark="props.remark"
        />

        <FormTextarea
            v-else-if="props.type === 'textarea'"
            :id="props.id"
            :label="props.label"
            :model-value="props.modelValue"
            @update:model-value="emit('update:modelValue', $event)"
            :placeholder="props.placeholder"
            :required="props.required"
            :error="props.error"
            :remark="props.remark"
            :textareaClass="props.fieldClass"
        />

        <div v-else-if="props.type === 'currency'">
            <Label :for="props.id" :class="props.labelClass">
                {{ props.label }} (RM)
                <RequiredIndicator v-if="props.required" />
            </Label>
            <Input
                v-if="props.type === 'currency'"
                :id="props.id"
                type="text"
                :model-value="currencyValue"
                @update:model-value="onCurrencyInput"
                :placeholder="props.placeholder"
                :required="props.required"
                :maxlength="props.maxlength"
                :class="cn('mt-1 w-full', props.fieldClass)"
            />
            <InputError class="mt-1" :message="props.error" />
        </div>

        <FormInputWithDropdown
            v-else-if="props.type === 'inputSelect'"
            :label="props.label"
            :modelValue="props.modelValue"
            @update:model-value="emit('update:modelValue', $event)"
            :required="props.required"
            :error="props.error"
            :labelClass="props.labelClass"
            :selectPosition="props.selectPosition"
            :inputPlaceholder="props.placeholder"
            :options="props.options"
            :selectPlaceholder="props.selectPlaceholder"
        />

        <FormRadio
            v-else-if="props.type === 'radio'"
            :id="props.id"
            :label="props.label"
            :modelValue="props.modelValue"
            @update:model-value="emit('update:modelValue', $event)"
            :required="props.required"
            :error="props.error"
            :labelClass="props.labelClass"
            :options="props.options"
        />
        <div v-if="props.type === 'date'">
            <Label :for="props.id" :class="props.labelClass">
                {{ props.label }}
                <RequiredIndicator v-if="props.required" />
            </Label>
            <Calendar :modelValue="props.modelValue" @update:model-value="emit('update:modelValue', $event)" :placeholderLabel="props.placeholder" />
        </div>

        <div v-if="props.type === 'input_search'">
            <Label :for="props.id" :class="props.labelClass">
                {{ props.label }}
                <RequiredIndicator v-if="props.required" />
            </Label>

            <FormInputSearch
                :api="props.api || ''"
                @resultsUpdated="handleResults"
                :labelKeywords="props.labelKeywords || []"
                :placeholder="props.placeholder"
                iconPosition="right"
                :defaultSearchValue="props.defaultSearchValue"
                :icons="props.icons"
                :params="props.params"
            ></FormInputSearch>

            <p v-if="props.remark !== ''" class="text-muted-foreground text-sm">
                {{ props.remark }}
            </p>
            <InputError class="mt-1" :message="props.error" />
        </div>
    </div>
</template>
