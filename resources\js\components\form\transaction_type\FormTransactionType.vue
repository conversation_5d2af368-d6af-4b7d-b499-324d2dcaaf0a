<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import { CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import { computed, onMounted, ref, watch } from 'vue';

interface Option {
    id: string | number;
    value: string;
}

interface Props {
    form: any;
    loan: any;
    from: string;
    transactionTypes?: Option[];
    letterTypes?: Option[];
    placeholder?: string;
    required?: boolean;
    maxlength?: string | number;
    error?: string;
    class?: string;
    labelClass?: string;
    fieldClass?: string;
    multiple?: boolean;
    useMultiselect?: boolean;
    remark?: string;
    selectPlaceholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Select',
    required: false,
    maxlength: 255,
    class: 'gap-2',
    labelClass: '',
    fieldClass: '',
    options: () => [],
    multiple: false,
    useMultiselect: false,
    remark: '',
});

const emit = defineEmits(['update:modelValue']);

const currencyValue = ref('');

watch(
    () => props.modelValue,
    (val) => {
        if (props.type === 'currency') {
            const strVal = String(val || '')
                .replace(/\D/g, '')
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            currencyValue.value = strVal;
        }
    },
    { immediate: true },
);

const onCurrencyInput = (val: string | number) => {
    const strVal = String(val);
    const numeric = strVal.replace(/\D/g, '');
    emit('update:modelValue', numeric);
};

const handleChange = (value: string | number | (string | number)[]) => {
    emit('update:modelValue', value);
};

const OFFICE_PAYMENT_ID = props.transactionTypes?.find((type) => type.value === 'Official Receipt - Repayment')?.id ?? null;
const POSTAGE_CHARGES_ID = props.transactionTypes?.find((type) => type.value === 'Postage Charges')?.id ?? null;
const MISC_CHARGES_ID = props.transactionTypes?.find((type) => type.value === 'Misc Charges')?.id ?? null;
const LEGAL_FEE_ID = props.transactionTypes?.find((type) => type.value === 'Legal Fee (Letter of Demand)')?.id ?? null;
const REPAYMENT_REBATE_ID = props.transactionTypes?.find((type) => type.value === 'Repayment + Rebate')?.id ?? null;
const NOTICE_DEMAND_ID = props.letterTypes?.find((type) => type.value === 'Notice of Demand')?.id ?? null;

onMounted(() => {
    const defaultOption = props.transactionTypes?.find((option) => option.value === 'Official Receipt - Repayment');
    if (defaultOption) {
        props.form.selection_transaction_type_id = defaultOption.id;
    }
});

const officePaymentId = computed(() => props.form.selection_transaction_type_id === OFFICE_PAYMENT_ID);

const postageCharges = computed(() => props.form.selection_transaction_type_id === POSTAGE_CHARGES_ID);

const noticeDemand = computed(() => props.form.selection_letter_type_id === NOTICE_DEMAND_ID);

const miscCharges = computed(() => props.form.selection_transaction_type_id === MISC_CHARGES_ID);

const legalFee = computed(() => props.form.selection_transaction_type_id === LEGAL_FEE_ID);

const repaymentRebate = computed(() => props.form.selection_transaction_type_id === REPAYMENT_REBATE_ID);

const rebateOptions = computed(() =>
    Array.from({ length: props.loan.loanDetail.no_of_instalment || 0 }, (_, i) => ({
        label: String(i + 1),
        value: i + 1,
    })),
);
</script>

<template>
    <CardContent class="py-4">
        <template v-if="props.from == 'edit'">
            <Label class="text-[20px]">Transaction Details</Label>
            <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                <div>
                    <Label for="">Transaction Type</Label>
                    <Select v-model="props.form.selection_transaction_type_id">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Select Transaction Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="type in props.transactionTypes" :key="type.id" :value="type.id">
                                {{ type.value }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div></div>
                <div v-if="postageCharges">
                    <Label for="">Instalment No.</Label>
                    <Select v-model="props.form.no_instalment">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Select Instalment No" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="n in props.loan.loanDetail.no_of_instalment" :key="n" :value="n">
                                {{ n }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div v-if="postageCharges">
                    <Label for="">Letter Type</Label>
                    <Select v-model="props.form.selection_letter_type_id">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Select Letter Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="type in props.letterTypes" :key="type.id" :value="type.id">
                                {{ type.value }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <template v-if="noticeDemand">
                    <div>
                        <Label for="">AR Serial No.</Label>
                        <Input
                            id="ar-serial-no"
                            v-model="props.form.ar_serial_no"
                            :error="props.form.errors.ar_serial_no"
                            required
                            placeholder="AR Serial No"
                        />
                    </div>
                    <div></div>
                </template>
                <div v-if="officePaymentId || postageCharges || miscCharges || legalFee || repaymentRebate">
                    <Label for="">Amount</Label>
                    <Input id="amount" v-model="props.form.amount" :error="props.form.errors.amount" required placeholder="Amount" />
                </div>
                <div v-if="officePaymentId || postageCharges || miscCharges || legalFee || repaymentRebate">
                    <Label for="">Payment Date</Label>
                    <Calendar v-model="props.form.payment_date" :error="props.form.errors.payment_date" required placeholderLabel="Payment Date" />
                </div>
                <div v-if="officePaymentId || repaymentRebate">
                    <Label for="">Payment Method</Label>
                    <Select v-model="props.form.selection_payment_method_id">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Payment Method" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="type in props.options" :key="type.id" :value="type.id">
                                {{ type.value }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div v-if="officePaymentId || repaymentRebate">
                    <Label for="">Payment Ref No.</Label>
                    <Calendar
                        v-model="props.form.payment_ref_no"
                        :error="props.form.errors.payment_ref_no"
                        required
                        placeholderLabel="Payment Ref No."
                    />
                </div>
                <template v-if="repaymentRebate">
                    <div>
                        <Label for="">Rebate Amount</Label>
                        <Input
                            id="rebate-amount"
                            v-model="props.form.rebate_amount"
                            :error="props.form.errors.rebate_amount"
                            required
                            placeholder="Rebate Amount"
                        />
                    </div>
                    <div>
                        <Label for="">Rebate Tenure</Label>
                        <div class="relative">
                            <Multiselect
                                id="rebate-tenure"
                                :options="rebateOptions"
                                :multiple="true"
                                :model-value="props.form.rebate_tenure"
                                @update:model-value="handleChange"
                                searchable
                                :close-on-select="false"
                                :clear-on-select="false"
                                :preserve-search="true"
                                placeholder="Rebate Tenure"
                                :class="'h-[1px] w-full shadow-xs'"
                                mode="tags"
                                :hideSelected="false"
                                :showOptions="true"
                                :canClear="false"
                                :canDeselect="true"
                                :openDirection="'bottom'"
                                :showLabels="false"
                                :valueProp="'value'"
                                :labelProp="'label'"
                            >
                                <!-- Custom option template with checkbox style -->
                                <template #option="{ option, selected }">
                                    <div class="flex items-center gap-2">
                                        <div class="flex h-5 w-5 items-center justify-center">
                                            <div v-if="selected" class="bg-steel flex h-4 w-4 items-center justify-center rounded-sm"></div>
                                        </div>
                                        <span>{{ option.label }}</span>
                                    </div>
                                </template>
                            </Multiselect>
                        </div>
                    </div>
                </template>
                <div v-if="officePaymentId || miscCharges || legalFee || repaymentRebate" class="col-span-2">
                    <Label for="">Receipt Description</Label>
                    <Input
                        id="description"
                        v-model="props.form.description"
                        :error="props.form.errors.description"
                        required
                        placeholder="Receipt Description"
                    />
                </div>
            </div>
        </template>
        <div v-if="officePaymentId || repaymentRebate || props.from == 'show'" class="grid grid-cols-1 gap-3 lg:grid-cols-1">
            <div class="space-y-3">
                <Label class="pt-2 text-[20px]" for="">Current Instalment Info</Label>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Current Instalment No</Label>
                    <p>123</p>
                </div>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Instalment Owe (RM)</Label>
                    <p>123</p>
                </div>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Late Charges (RM)</Label>
                    <p>123</p>
                </div>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Postage Charges (RM)</Label>
                    <p>123</p>
                </div>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Legal Fee (RM)</Label>
                    <p>123</p>
                </div>
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base font-normal">Misc Charges (RM)</Label>
                    <p>123</p>
                </div>
                <Separator class="my-4" />
                <div class="flex items-center justify-between">
                    <Label for="" class="text-base">Total (RM)</Label>
                    <p>123</p>
                </div>
            </div>
        </div>
        <Label class="pt-3 text-[20px]" for="">Unpaid Payment</Label>
    </CardContent>
</template>
