<?php

namespace App\Http\Resources\Loans;

use App\Traits\HandlesFileStorage;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoanDetailResource extends JsonResource
{
    use HandlesFileStorage;

    /**
     * Transform the detail resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request, bool $detailed = false): array
    {
        $customer = [];
        foreach ($this->loanCustomerProfiles as $loanCustomer) {
            $contacts = [];

            foreach ($loanCustomer->loanCustomerContacts as $loanCustomerContact) {
                foreach ($loanCustomerContact->contacts as $contact) {
                    $contacts[] = [
                        'selection_type_id' => $contact->selection_type_id,
                        'type' => $contact->type,
                        'selection_country_id' => $contact->selection_country_id,
                        'country' => $contact->country,
                        'country_selection' => $contact->contactCountrySelection ? $contact->contactCountrySelection->value : null,
                        'contact' => $contact->contact,
                    ];
                }
            }

            $addresses = [];
            foreach ($loanCustomer->loanCustomerAddresses as $loanCustomerAddress) {
                foreach ($loanCustomerAddress->addresses as $address) {
                    $addresses[] = [
                        'id' => $address->id,
                        'selection_type_id' => $address->selection_type_id,
                        'type' => $address->type,
                        'line_1' => $address->line_1,
                        'line_2' => $address->line_2,
                        'postcode' => $address->postcode,
                        'city' => $address->city,
                        'selection_state_id' => $address->selection_state_id,
                        'state' => $address->state,
                        'state_selection' => $address->stateSelection ? $address->stateSelection->value : null,
                        'selection_country_id' => $address->selection_country_id,
                        'country' => $address->country,
                        'country_selection' => $address->countrySelection ? $address->countrySelection->value : null,
                    ];
                }
            }

            $customer[] = [
                'id' => $loanCustomer->id,
                'uuid' => $loanCustomer->uuid,
                'customer_id' => $loanCustomer->customer_id,
                'name' => $loanCustomer->name,
                'identity_no' => $loanCustomer->identity_no ?? null,
                'email' => $loanCustomer->email,
                'old_identity_no' => $loanCustomer->old_identity_no ?? null,
                'registration_date' => $loanCustomer->registration_date ?? null,
                'years_of_incorporation' => $loanCustomer->years_of_incorporation ?? null,
                'age' => $loanCustomer->age ?? null,
                'birth_date' => $loanCustomer->birth_date ?? null,
                'selection_type_id' => $loanCustomer->selection_type_id,
                'customer_type' => $loanCustomer->type,
                'selection_gender_id' => $loanCustomer->selection_gender_id,
                'gender' => $loanCustomer->gender,
                'selection_race_id' => $loanCustomer->selection_race_id,
                'race' => $loanCustomer->race,
                'selection_nationality_id' => $loanCustomer->selection_nationality_id,
                'nationality' => $loanCustomer->nationality,
                'selection_education_level_id' => $loanCustomer->selection_education_level_id,
                'education_level' => $loanCustomer->education_level,
                'selection_marriage_status_id' => $loanCustomer->selection_marriage_status_id,
                'marriage_status' => $loanCustomer->marriage_status,
                'remark' => $loanCustomer->remark,
                'is_primary' => $loanCustomer->is_primary,
                'employment' => $loanCustomer->loanCustomerEmployment ? [
                    'id' => $loanCustomer->loanCustomerEmployment->id,
                    'employer_name' => $loanCustomer->loanCustomerEmployment->employer_name,
                    'length_service_year' => $loanCustomer->loanCustomerEmployment->length_service_year,
                    'length_service_month' => $loanCustomer->loanCustomerEmployment->length_service_month,
                    'job_position' => $loanCustomer->loanCustomerEmployment->job_position,
                    'selection_terms_of_employment_id' => $loanCustomer->loanCustomerEmployment->selection_terms_id,
                    'terms_of_employment' => $loanCustomer->loanCustomerEmployment->terms,
                    'terms_of_employment_selection' => $loanCustomer->loanCustomerEmployment->selectionTermsOfEmployment ? $loanCustomer->loanCustomerEmployment->selectionTermsOfEmployment->value : null,
                    'selection_occupation_id' => $loanCustomer->loanCustomerEmployment->selection_occupation_id,
                    'occupation' => $loanCustomer->loanCustomerEmployment->occupation,
                    'occupation_selection' => $loanCustomer->loanCustomerEmployment->selectionOccupation ? $loanCustomer->loanCustomerEmployment->selectionOccupation->value : null,
                    'selection_business_classification_id' => $loanCustomer->loanCustomerEmployment->selection_business_classification_id,
                    'business_classification' => $loanCustomer->loanCustomerEmployment->business_classification,
                    'business_classification_selection' => $loanCustomer->loanCustomerEmployment->selectionBusinessClassification ? $loanCustomer->loanCustomerEmployment->selectionBusinessClassification->value : null,
                    'gross_income' => $loanCustomer->loanCustomerEmployment->gross_income,
                    'net_income' => $loanCustomer->loanCustomerEmployment->net_income,
                    'telephone' => $loanCustomer->loanCustomerEmployment->contacts->firstWhere('category', 1)->contact ?? null,
                    'telephone_country_selection' => $loanCustomer->loanCustomerEmployment->contacts->firstWhere('category', 1)->country ?? null,
                    'mobile_phone' => $loanCustomer->loanCustomerEmployment->contacts->firstWhere('category', 2)->contact ?? null,
                    'mobile_country_selection' => $loanCustomer->loanCustomerEmployment->contacts->firstWhere('category', 2)->country ?? null,
                    'address' => $loanCustomer->loanCustomerEmployment->address ?? null,
                ] : null,
                'company' => $loanCustomer->loanCustomerCompany ? [
                    'id' => $loanCustomer->loanCustomerCompany->id,
                    'uuid' => $loanCustomer->loanCustomerComapny->uuid,
                    'current_paid_up_capital' => $loanCustomer->loanCustomerCompany->current_paid_up_capital,
                    'business_turnover' => $loanCustomer->loanCustomerCompany->business_turnover,
                    'business_turnover_date' => $loanCustomer->loanCustomerCompany->business_turnover_date,
                    'business_net_income' => $loanCustomer->loanCustomerCompany->business_net_income,
                    'business_net_income_date' => $loanCustomer->loanCustomerCompany->business_net_income_date,
                    'selection_nature_of_business_id' => $loanCustomer->loanCustomerCompany->selection_nature_of_business_id,
                    'nature_of_business' => $loanCustomer->loanCustomerCompany->nature_of_business,
                    'nature_of_business_selection' => $loanCustomer->loanCustomerCompany->selectionNatureOfBusiness ? $loanCustomer->loanCustomerCompany->selectionNatureOfBusiness->value : null,
                    'selection_country_of_business_id' => $loanCustomer->loanCustomerCompany->selection_country_of_business_id,
                    'country_of_business' => $loanCustomer->loanCustomerCompany->country_of_business,
                    'country_of_business_selection' => $loanCustomer->loanCustomerCompany->selectionCountryOfBusiness ? $loanCustomer->loanCustomerCompany->selectionCountryOfBusiness->value : null,
                ] : null,
                'contacts' => $contacts,
                'addresses' => $addresses,
            ];
        }

        $loanCustomerCollaterals = [];
        foreach ($this->loanCustomerCollaterals as $loanCustomerCollateral) {

            $valuers = [];
            foreach ($loanCustomerCollateral->collateral->valuers as $valuer) {
                $valuers[] = [
                    'id' => $valuer->id,
                    'valuation_amount' => $valuer->valuation_amount,
                    'valuer' => $valuer->valuer,
                    'valuation_received_date' => $valuer->valuation_received_date,
                    'land_search_received_date' => $valuer->land_search_received_date,
                ];
            }

            $propertyOwners = [];
            foreach ($loanCustomerCollateral->collateral->property->propertyOwners as $owner) {
                $propertyOwners[] = [
                    'id' => $owner->id,
                    'name' => $owner->name,
                    'identity_no' => $owner->identity_no,
                    'selection_telephone_country_id' => $owner->telephone()?->selection_country_id,
                    'selection_mobile_country_id' => $owner->mobilePhone()?->selection_country_id,
                    'telephone_country_selection' => $owner->telephone()?->contactCountrySelection ? $owner->telephone()->contactCountrySelection->value : null,
                    'mobile_country_selection' => $owner->mobilePhone()?->contactCountrySelection ? $owner->mobilePhone()->contactCountrySelection->value : null,
                    'telephone' => $owner->telephone,
                    'mobile_phone' => $owner->mobilePhone,
                ];
            }

            $loanCustomerCollaterals[] = [
                'id' => $loanCustomerCollateral->collateral->id,
                'selection_customer_type_id' => $loanCustomerCollateral->collateral->selection_customer_type_id,
                'customer_type' => $loanCustomerCollateral->collateral->customer_type,
                'customer_type_selection' => $loanCustomerCollateral->collateral->customerTypeSelection ? $loanCustomerCollateral->collateral->customerTypeSelection->value : null,
                'selection_type_id' => $loanCustomerCollateral->collateral->selection_type_id,
                'type_selection' => $loanCustomerCollateral->collateral->typeSelection ? $loanCustomerCollateral->collateral->typeSelection->value : null,
                'name' => $loanCustomerCollateral->collateral->name,
                'identity_no' => $loanCustomerCollateral->collateral->identity_no,
                'company_name' => $loanCustomerCollateral->collateral->company_name,
                'business_registration_no' => $loanCustomerCollateral->collateral->business_registration_no,
                'status' => $loanCustomerCollateral->collateral->status->value,
                'status_label' => $loanCustomerCollateral->collateral->status->label(),
                'remark' => $loanCustomerCollateral->collateral->remark,
                'property' => $loanCustomerCollateral->collateral->property ? [
                    'id' => $loanCustomerCollateral->collateral->property->id,
                    'ownership_no' => $loanCustomerCollateral->collateral->property->ownership_no,
                    'lot_number' => $loanCustomerCollateral->collateral->property->lot_number,
                    'selection_land_category_id' => $loanCustomerCollateral->collateral->property->selection_land_category_id,
                    'land_category' => $loanCustomerCollateral->collateral->property->land_category,
                    'land_category_selection' => $loanCustomerCollateral->collateral->property->landCategorySelection ? $loanCustomerCollateral->collateral->property->landCategorySelection->value : null,
                    'land_category_other' => $loanCustomerCollateral->collateral->property->land_category_other,
                    'type_of_property' => $loanCustomerCollateral->collateral->property->type_of_property,
                    'selection_type_of_property_id' => $loanCustomerCollateral->collateral->property->selection_type_of_property_id,
                    'type_of_property_selection' => $loanCustomerCollateral->collateral->property->propertyTypesSelection ? $loanCustomerCollateral->collateral->property->propertyTypesSelection->value : null,
                    'selection_land_size_unit' => $loanCustomerCollateral->collateral->property->selection_land_size_unit,
                    'land_size_unit_selection' => $loanCustomerCollateral->collateral->property->landSizeSelection ? $loanCustomerCollateral->collateral->property->landSizeSelection->value : null,
                    'land_size' => $loanCustomerCollateral->collateral->property->land_size,
                    'selection_land_status_id' => $loanCustomerCollateral->collateral->property->selection_land_status_id,
                    'land_status' => $loanCustomerCollateral->collateral->property->land_status,
                    'land_status_selection' => $loanCustomerCollateral->collateral->property->landStatusSelection ? $loanCustomerCollateral->collateral->property->landStatusSelection->value : null,
                    'no_syit_piawai' => $loanCustomerCollateral->collateral->property->no_syit_piawai,
                    'certified_plan_no' => $loanCustomerCollateral->collateral->property->certified_plan_no,
                    'selection_built_up_area_unit' => $loanCustomerCollateral->collateral->property->selection_built_up_area_unit,
                    'built_up_area_of_property' => $loanCustomerCollateral->collateral->property->built_up_area_of_property,
                    'built_up_area_unit_selection' => $loanCustomerCollateral->collateral->property->builtUpAreaSelection ? $loanCustomerCollateral->collateral->property->builtUpAreaSelection->value : null,
                    'city' => $loanCustomerCollateral->collateral->property->city,
                    'location' => $loanCustomerCollateral->collateral->property->location,
                    'district' => $loanCustomerCollateral->collateral->property->district,
                    'address' => $loanCustomerCollateral->collateral->property->address ? [
                        'id' => $loanCustomerCollateral->collateral->property->address->id,
                        'line_1' => $loanCustomerCollateral->collateral->property->address->line_1,
                        'line_2' => $loanCustomerCollateral->collateral->property->address->line_2,
                        'postcode' => $loanCustomerCollateral->collateral->property->address->postcode,
                        'city' => $loanCustomerCollateral->collateral->property->address->city,
                        'state' => $loanCustomerCollateral->collateral->property->address->state,
                        'selection_state_id' => $loanCustomerCollateral->collateral->property->address->selection_state_id,
                        'state_selection' => $loanCustomerCollateral->collateral->property->address->stateSelection ? $loanCustomerCollateral->collateral->property->address->stateSelection->value : null,
                        'country' => $loanCustomerCollateral->collateral->property->address->country,
                        'selection_country_id' => $loanCustomerCollateral->collateral->property->address->selection_country_id,
                        'country_selection' => $loanCustomerCollateral->collateral->property->address->countrySelection ? $loanCustomerCollateral->collateral->property->address->countrySelection->value : null,
                    ] : null,
                ] : null,
                'owners' => $propertyOwners,
                'valuers' => $valuers,
            ];
        }

        $emergency = null;

        foreach ($this->loanEmergencies as $loanEmergency) {
            $contact = [];
            $address = [];

            foreach ($loanEmergency->loanEmergencyDetails as $loanEmergencyDetail) {
                foreach ($loanEmergencyDetail->contact as $loanEmergencyContact) {
                    $contact[] = [
                        'id' => $loanEmergencyContact->id,
                        'selection_type_id' => $loanEmergencyContact->selection_type_id,
                        'type' => $loanEmergencyContact->type,
                        'contact' => $loanEmergencyContact->contact,
                        'selection_country_id' => $loanEmergencyContact->selection_country_id,
                        'country_selection' => $loanEmergencyContact->contactCountrySelection ? $loanEmergencyContact->contactCountrySelection->value : null,
                    ];
                }

                foreach ($loanEmergencyDetail->address as $loanEmergencyAddress) {
                    $address[] = [
                        'id' => $loanEmergencyAddress->id,
                        'line_1' => $loanEmergencyAddress->line_1,
                        'line_2' => $loanEmergencyAddress->line_2,
                        'postcode' => $loanEmergencyAddress->postcode,
                        'city' => $loanEmergencyAddress->city,
                        'selection_state_id' => $loanEmergencyAddress->selection_state_id,
                        'state' => $loanEmergencyAddress->state,
                        'state_selection' => $loanEmergencyAddress->stateSelection ? $loanEmergencyAddress->stateSelection->value : null,
                        'selection_country_id' => $loanEmergencyAddress->selection_country_id,
                        'country' => $loanEmergencyAddress->country,
                        'country_selection' => $loanEmergencyAddress->countrySelection ? $loanEmergencyAddress->countrySelection->value : null,
                    ];
                }
            }

            $emergency = [
                'id' => $loanEmergency->id,
                'name' => $loanEmergency->name,
                'identity_no' => $loanEmergency->identity_no,
                'age' => $loanEmergency->age,
                'birth_date' => $loanEmergency->birth_date,
                'employment_name' => $loanEmergency->employment_name,
                'selection_gender_id' => $loanEmergency->selection_gender_id,
                'gender' => $loanEmergency->gender,
                'gender_selection' => $loanEmergency->selectionGender ? $loanEmergency->selectionGender->value : null,
                'selection_relationship_id' => $loanEmergency->selection_relationship_id,
                'relationship' => $loanEmergency->relationship,
                'relationship_selection' => $loanEmergency->selectionRelationship ? $loanEmergency->selectionRelationship->value : null,
                'selection_nationality_id' => $loanEmergency->selection_nationality_id,
                'nationality' => $loanEmergency->nationality,
                'nationality_selection' => $loanEmergency->selectionNationality ? $loanEmergency->selectionNationality->value : null,
                'addresses' => $address,
                'contacts' => $contact,
            ];
        }

        $guarantors = [];
        foreach ($this->loanGuarantors as $loanGuarantor) {
            $guarantorContacts = [];

            foreach ($loanGuarantor->loanGuarantorDetails as $loanGuarantorDetail) {
                foreach ($loanGuarantorDetail->contact as $loanGuarantorContact) {
                    $guarantorContacts[] = [
                        'id' => $loanGuarantorContact->id,
                        'selection_type_id' => $loanGuarantorContact->selection_type_id,
                        'type' => $loanGuarantorContact->type,
                        'contact' => $loanGuarantorContact->contact,
                        'selection_country_id' => $loanGuarantorContact->selection_country_id,
                        'country_selection' => $loanGuarantorContact->contactCountrySelection ? $loanGuarantorContact->contactCountrySelection->value : null,
                    ];
                }
            }

            $guarantors[] = [
                'id' => $loanGuarantor->id,
                'name' => $loanGuarantor->name,
                'identity_no' => $loanGuarantor->identity_no,
                'age' => $loanGuarantor->age,
                'birth_date' => $loanGuarantor->birth_date,
                'selection_gender_id' => $loanGuarantor->selection_gender_id,
                'gender' => $loanGuarantor->gender,
                'gender_selection' => $loanGuarantor->selectionGender ? $loanGuarantor->selectionGender->value : null,
                'selection_relationship_id' => $loanGuarantor->selection_relationship_id,
                'relationship' => $loanGuarantor->relationship,
                'relationship_selection' => $loanGuarantor->selectionRelationship ? $loanGuarantor->selectionRelationship->value : null,
                'selection_nationality_id' => $loanGuarantor->selection_nationality_id,
                'nationality' => $loanGuarantor->nationality,
                'nationality_selection' => $loanGuarantor->selectionNationality ? $loanGuarantor->selectionNationality->value : null,
                'employment_name' => $loanGuarantor->employment_name,
                'length_service_year' => $loanGuarantor->length_service_year,
                'length_service_month' => $loanGuarantor->length_service_month,
                'job_position' => $loanGuarantor->job_position,
                'selection_terms_of_employment_id' => $loanGuarantor->selection_terms_of_employment_id,
                'terms_of_employment' => $loanGuarantor->terms_of_employment,
                'terms_of_employment_selection' => $loanGuarantor->selectionTermsOfEmployment ? $loanGuarantor->selectionTermsOfEmployment->value : null,
                'selection_occupation_id' => $loanGuarantor->selection_occupation_id,
                'occupation' => $loanGuarantor->occupation,
                'occupation_selection' => $loanGuarantor->selectionOccupation ? $loanGuarantor->selectionOccupation->value : null,
                'selection_business_classification_id' => $loanGuarantor->selection_business_classification_id,
                'business_classification' => $loanGuarantor->business_classification,
                'business_classification_selection' => $loanGuarantor->selectionBusinessClassification ? $loanGuarantor->selectionBusinessClassification->value : null,
                'contacts' => $guarantorContacts,
                'addresses' => $address,
            ];
        }

        $bankDetails = [];
        if ($this->status->value >= 5) {
            foreach ($this->loanBankAccounts as $loanBankAccount) {
                $bankDetails[] = [
                    'id' => $loanBankAccount->id,
                    'uuid' => $loanBankAccount->uuid,
                    'selection_bank_id' => $loanBankAccount->selection_bank_id,
                    'bank' => $loanBankAccount->bank,
                    'selection_type_id' => $loanBankAccount->selection_type_id,
                    'type' => $loanBankAccount->type,
                    'account_no' => $loanBankAccount->account_no,
                    'account_name' => $loanBankAccount->account_name,
                ];
            }
        }

        $documents = [];

        foreach ($this->loanDocuments as $loanDocument) {
            foreach ($loanDocument->documents as $doc) {

                $path = $doc->url;
            }
            $fileContent = $this->getUploadedFile($path);

            $documents[] = [
                'id' => $doc->id,
                'url' => $path,
                'category' => $doc->category,
                'selection_type_id' => $loanDocument->selection_type_id,
                'file' => [
                    'name' => $fileContent['name'] ?? null,
                    'size' => $fileContent['size'] ?? null,
                    'url' => $fileContent['url'] ?? null,
                ],
                'created_at' => $doc->created_at,
                'uploaded_by' => $doc->createdBy ? [
                    'id' => $doc->createdBy->id,
                    'username' => $doc->createdBy->username,
                ] : null,
            ];
        }

        $baseData = [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'customer_id' => $this->customer_id,
            'customer' => $customer,
            'company_id' => $this->company_id,
            'company' => $this->company,
            'team_id' => $this->team_id,
            'team' => $this->team,
            'agent_id' => $this->agent_id,
            'agent' => $this->agent,
            'selection_type_id' => $this->selection_type_id,
            'type' => $this->type,
            'status' => $this->status->value,
            'loan_customer_collaterals' => $loanCustomerCollaterals,
            'loanDetail' => $this->loanDetail ? [
                'id' => $this->loanDetail->id,
                'uuid' => $this->loanDetail->uuid,
                'selection_mode_type_id' => $this->loanDetail->selection_mode_type_id,
                'mode_type' => $this->loanDetail->mode_type,
                'loan_principle_amount' => $this->loanDetail->loan_principle_amount,
                'last_payment' => $this->loanDetail->last_payment,
                'instalment_amount' => $this->loanDetail->instalment_amount,
                'interest' => $this->loanDetail->interest,
                'no_of_instalment' => $this->loanDetail->no_of_instalment,
                'selection_repayment_method_id' => $this->loanDetail->selection_repayment_method_id,
                'repayment_method' => $this->loanDetail->repayment_method,
                'stamp_duty' => $this->loanDetail->stamp_duty ?? null,
                'attenstation_fee' => $this->loanDetail->attenstation_fee ?? null,
                'legal_fee' => $this->loanDetail->legal_fee ?? null,
                'processing_fee' => $this->loanDetail->processing_fee ?? null,
                'misc_charges' => $this->loanDetail->misc_charges ?? null,
                'late_payment_charges' => $this->loanDetail->late_payment_charges ?? null,
                'rebate' => $this->loanDetail->rebate ?? null,
                'loan_disbursement_amount' => $this->loanDetail->loan_disbursement_amount,
                'commencement_date' => $this->loanDetail->commencement_date,
            ] : null,
            'emergency' => $emergency,
            'guarantors' => $guarantors,
            'bankDetails' => $bankDetails,
            'documents' => $documents,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];

        return $baseData;
    }
}
