import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { App } from 'vue';

// Import icons by category
import {
    // Buttons
    faAngleLeft,
    faArrowRotateRight,
    // Interface
    faBars,
    // Agent Management
    faBook,
    faBuilding,
    faCalendar,
    // Status
    faCheck,
    faChevronLeft,
    // Navigation
    faChevronRight,
    faCircleUser,
    faClipboardCheck,
    faClipboardList,
    faCloudArrowUp,
    faEdit,
    faEnvelope,
    faExclamation,
    faEye,
    faFile,
    faFileContract,
    faFilePdf,
    faFloppyDisk,
    // Dashboard
    faGaugeHigh,
    faGavel,
    // Collateral Management
    faGear,
    faHouseChimneyUser,
    faHouseUser,
    faIdCard,
    faInfo,
    faListCheck,
    faMagnifyingGlass,
    faMinus,
    faNoteSticky,
    faPaperPlane,
    faPen,
    faPlus,
    faRetweet,
    faRightLeft,
    // Role Management
    faRobot,
    // Loan Management
    faSackDollar,
    // Actions
    faSave,
    //System Management
    faScrewdriverWrench,
    //Sort
    faSort,
    faSortDown,
    faSortUp,
    faTerminal,
    faTimes,
    faTrash,
    // User Management
    faUser,
    faUserGear,
    faUserGroup,
    faUserPlus,
    faUsers,
    faUserSlash,
    faUserTie,
    faX,
} from '@fortawesome/free-solid-svg-icons';

// Add all icons to library
library.add(
    faGaugeHigh,
    faUser,
    faUsers,
    faUserGroup,
    faUserPlus,
    faUserGear,
    faHouseChimneyUser,
    faSackDollar,
    faScrewdriverWrench,
    faTerminal,
    faRobot,
    faBuilding,
    faUserTie,
    faBook,
    faListCheck,
    faClipboardCheck,
    faGear,
    faFileContract,
    faChevronRight,
    faChevronLeft,
    faBars,
    faTimes,
    faEnvelope,
    faSave,
    faTrash,
    faEdit,
    faCheck,
    faExclamation,
    faInfo,
    faPaperPlane,
    faAngleLeft,
    faArrowRotateRight,
    faMagnifyingGlass,
    faPlus,
    faUserSlash,
    faEye,
    faPen,
    faSort,
    faSortDown,
    faSortUp,
    faMinus,
    faCalendar,
    faFile,
    faCloudArrowUp,
    faFilePdf,
    faIdCard,
    faCircleUser,
    faNoteSticky,
    faRightLeft,
    faX,
    faHouseUser,
    faGavel,
    faClipboardList,
    faRetweet,
    faFloppyDisk,
);

// Create a Vue plugin
export function installFontAwesome(app: App) {
    app.component('FontAwesomeIcon', FontAwesomeIcon);
}

// Export the component for direct use
export { FontAwesomeIcon };
